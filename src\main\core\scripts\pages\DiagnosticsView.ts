import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';
import { BatteryData, BatteryDataOut, BatteryAreaInfo, BatteryDamageInfo, BatteryType, batteryDataHelper } from "../interfaces/batteryData";//, DiagnosisTips
import { DevicePidVid, ChargerData, chargerDataHelper } from "../interfaces/chargerData";
// import { resolveResource } from '@tauri-apps/api/path';
// import { convertFileSrc } from '@tauri-apps/api/core';
import { getBatteryType, getBatteryTypeText } from "../../dataContainer/BatteryDataContainer";
import { BatteryValidator } from "../validators";
import { BatteryApiService } from "../services/BatteryApiServices";
import { ChargerApiService } from "../services/ChargerApiServices";

console.log("DiagnosticsView.ts 已載入");


let tbxProduct = "NA";
let tbxFirmwareVersion = "NA"; // 預設值為 NA，表示未取得資料
let tbxSerialNumber = "NA";

// 全域變數儲存電池資料
let currentBatteryData: BatteryData[] = [];
var timeoutID = -1;

// 創建 DiagnosticsViewController 實例
let diagnosticsController: DiagnosticsViewController | null = null;

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 添加錯誤圖示的 CSS 樣式
function addErrorIconStyles() {
  const styleId = 'battery-error-icons-style';
  if (document.getElementById(styleId)) return; // 避免重複添加

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    .error-icons {
      display: flex;
      gap: 2px;
      margin-top: 2px;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
    }

    .error-icon {
      font-size: 12px;
      cursor: help;
      padding: 1px 2px;
      border-radius: 2px;
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.3);
      transition: all 0.2s ease;
      display: inline-block;
      line-height: 1;
    }

    .error-icon:hover {
      background: rgba(239, 68, 68, 0.2);
      border-color: rgba(239, 68, 68, 0.5);
      transform: scale(1.1);
    }

    .battery-card.has-errors {
      /* border: 1px solid rgba(239, 68, 68, 0.3); */
      /* box-shadow: 0 0 5px rgba(239, 68, 68, 0.2); */
      border: 4px solid rgb(239 68 68 / 89%);
      box-shadow: 0 0 5px rgba(239, 68, 68, 0.6);
      border-radius: 12px;
    }

    .battery-card.has-errors .battery-info-card {
      /*background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));*/
      /*background: linear-gradient(51deg, rgb(135 68 68 / 54%), rgb(220 38 38));*/
    }
  `;
  document.head.appendChild(style);
}

// 在頁面載入時添加樣式
document.addEventListener('DOMContentLoaded', addErrorIconStyles);


async function insertBattery(num: number, batteryData?: BatteryData) {
    const battery = document.getElementById(`battery${num}`);
    const slot = document.getElementById(`slot${num}`);
    // const info = document.getElementById(`info${num}`);
    // const card = document.createElement('div');
    
    if(!battery || !slot) return;
    if(!batteryData) return;

    //XL-Battery XL-Battery-rotate
    // 移除所有電池狀態 class
    //battery.classList.remove('battery-image', 'battery-image-rotate-180', 'battery-normal-image', 'battery-normal-image-rotate-180', 'battery-charger-image', 'battery-charger-image-rotate-180', 'battery-low-image', 'battery-low-image-rotate-180', 'battery-broken-image', 'battery-broken-image-rotate-180');
    let batteryClass = 'battery-image';

    battery.classList.remove('battery-image', 'battery-image-rotate-180', 'verify-normal', 'verify-normal-rotate-180', 'verify-error', 'verify-error-rotate-180');

    // const batteryCard = battery.querySelector(".battery-card"); // 移除現有的電池資訊卡片
    // if(batteryCard) {
    //   batteryCard.classList.remove('battery-image', 'battery-image-rotate-180', 'verify-normal');
    // }
    // console.log("batteryCard:", batteryCard);

    // 判斷電池是否完成讀取
    const isBatteryCompleted = isBatteryComplete(batteryData?.fullyChargedCapacity ?? 0, batteryData?.remainingCapacity ?? 0, batteryData?.deviceName ?? '');
    
    // 執行電池驗證以確定是否有錯誤
    if(isBatteryCompleted) {
        // 檢查是否為人為設定的毀損狀態
        //const isDamaged = getBatteryDamage(batteryData.sn || '');

        
        // 執行電池驗證
        const verificationResult = verifyBatteryData(batteryData);
        // if (isDamaged) {
        //     // 如果設定為毀損狀態，優先使用毀損圖片
        //     batteryClass = 'battery-broken-image';
        // } else 
        if (!verificationResult.isValid) {
            // 如果驗證失敗，使用驗證結果中的電池類別
            batteryClass = verificationResult.batteryClass;
        } else {
            // batteryClass = 'battery-normal-image';
            batteryClass = 'verify-normal';
        }
        
    }

    if (num === 0 || num === 2 || num === 4) {
        batteryClass += '-rotate-180';
    }

    battery.classList.add(batteryClass);
    
    // const batteryHealth = getBatteryHealth(batteryData?.fullyChargedCapacity ?? 0, batteryData?.designCapacity ?? 0);
    // if(isBatteryComplete(batteryData?.fullyChargedCapacity ?? 0, batteryData?.remainingCapacity ?? 0)){
    //   card.innerHTML = `
    //     <div class="battery-info" id="info${num}">
    //         <p>Serial: ${batteryData?.sn}</p>
    //         <p>Current: ${batteryData?.current} mA</p>
    //         <p>Voltage: ${batteryData?.voltage} mV</p>
    //         <p>Temp: ${batteryData?.temperature.toFixed(2)} °C</p>
    //         <p>Health: ${batteryHealth.toFixed(2)}%</p>
    //         <p>Cycle: ${batteryData?.cycle}</p>
    //     </div>
    //     <button class="details-btn" onclick="showDetails(${batteryData?.id})">Details</button>
    //   `;
    // }else{
    //   card.innerHTML = `
    //     <div class="battery-info" id="info${num}">
    //         <p>Serial: ${batteryData?.sn}</p>
    //     </div>
    //   `;
    // }
    
    // 清空現有內容
    battery.innerHTML = '';
    battery.appendChild(await createBatteryCard(num, batteryData));
    battery.style.visibility = 'visible';
    battery.classList.remove('out');
    battery.classList.add('in');
    slot.style.display = 'none';
}

function removeBattery(num: number) {
    const battery = document.getElementById(`battery${num}`);
    const slot = document.getElementById(`slot${num}`);

    if (!battery || !slot) return;

    battery.classList.remove('in');
    battery.classList.add('out');
    slot.style.display = 'block';
    // 等待動畫完成後隱藏（1秒 = transition 時間）
    setTimeout(() => {
        battery.style.visibility = 'hidden';
    }, 600);
}

async function showBatteries() {
  try {
    console.log("開始獲取電池資料...");
    /// 調用 Charger_GetBatteries 並儲存回傳資料
    const batteryDataList: BatteryData[] = await invoke<BatteryData[]>("Charger_GetBatteries");
    /// 顯示資料
    console.log("Battery Data:", batteryDataList);
    
    // 更新電池卡片顯示
    updateBatteryDisplay(batteryDataList);
    
    /// 逐項顯示每個電池的資料（保留舊的動畫效果，如果需要的話）
    // batteryDataList.forEach((data, index) => {
    //   console.log(`Battery ${index + 1}: ID=${data.id}, SN=${data.sn}`);
    //   if(data.sn !== "") {
    //     insertBattery(index + 1);
    //   }else{
    //     removeBattery(index + 1);
    //   }
    // });
    console.log("電池資料獲取完成");
  } catch (error) {
      console.error("獲取電池資料時發生錯誤:", error);
      // 停止定時器以防止持續錯誤
      if (timeoutID !== -1) {
        window.clearInterval(timeoutID);
        timeoutID = -1;
        console.log("已停止定時器以防止持續錯誤");
      }
  }
}

async function Initial() {
  (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'block';
  try {
    console.log("開始初始化...");
    
    // 清除現有的定時器
    if (timeoutID !== -1) {
      //window.clearInterval(timeoutID);
      timeoutID = -1;
      console.log("已清除現有定時器");
    }
    
    const vList = [0x0000FFFF, 0xFFFF0000, 0x03EB4736];
    for (const product of vList) {
      console.log(`嘗試初始化產品: 0x${product.toString(16)}`);
      
      if (await invoke("Initial", { product })) {
        console.log(`產品 0x${product.toString(16)} 初始化成功，等待 5 秒...`);
        await delay(300);

        // 獲取設備型號
        let deviceModel = "";
        if (Object.values(DevicePidVid).includes(product)) {
          deviceModel = chargerDataHelper.getDeviceModelName(product);
          
          // 更新 UI 顯示設備型號
          const deviceModelElement = document.getElementById('device-model');
          if (deviceModelElement) {
            deviceModelElement.textContent = deviceModel;
          }
        }
        
        tbxProduct = await invoke("Charger_GetName");
        tbxFirmwareVersion = await invoke("Charger_GetVersion");
        tbxSerialNumber = await invoke("Charger_GetSerialNumber");

        // 初始化 DiagnosticsViewController
        if (!diagnosticsController) {
          diagnosticsController = new DiagnosticsViewController();
          await diagnosticsController.initialize();
        }
        diagnosticsController.updateChargerData({
          projectName: tbxProduct,
          firmwareVersion: tbxFirmwareVersion,
          serialNumber: tbxSerialNumber,
          acStatus: "AC_OK",
        });
        
        console.log("Product:", tbxProduct, "Firmware:", tbxFirmwareVersion, "Serial:", tbxSerialNumber);
        
        // 先執行一次電池資料獲取測試
        console.log("執行初始電池資料獲取測試...");
        await showBatteries();
        
        // 如果測試成功，才開始定時器
        console.log("開始設定定時器，每 6 秒獲取一次電池資料");
        timeoutID = window.setInterval(() => showBatteries(), 1000);
        
        console.log("初始化完成");
        (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'none';
        return;
      }
    }
    (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'none';
    console.log("所有產品初始化失敗");
    tbxProduct = "NA";
    tbxFirmwareVersion = "NA";
    tbxSerialNumber = "NA";
  } catch (error) {
    console.error("初始化過程中發生錯誤:", error);
    // 確保清除定時器
    if (timeoutID !== -1) {
      window.clearInterval(timeoutID);
      timeoutID = -1;
    }
    tbxProduct = "ERROR";
    tbxFirmwareVersion = "ERROR";
    tbxSerialNumber = "ERROR";
  }

  
}

function verifyBatteryData(batteryData: BatteryData): {
  isValid: boolean;
  batteryClass: string;
  errorIcons: Array<{
    type: string;
    icon: string;
    message: string;
  }>;
  errorDescription: string;
} {
  const errorIcons: Array<{ type: string; icon: string; message: string; }> = [];
  let errorDescription = "";
  let isValid = true;
  // let batteryClass = 'battery-normal-image';
  let batteryClass = '';

  // 檢查電池外觀是否有毀損
  if(BatteryValidator.isAppearanceBroken(batteryData.sn)){
    isValid = false;
    // batteryClass = 'battery-broken-image';
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[Appearance Broke]";
    errorIcons.push({
      type: 'appearance',
      icon: '💥',
      message: 'Battery Appearance Damaged'
    });
  }

  // 檢查電池序列號
  if(!BatteryValidator.isSerialNumberValid(batteryData.sn)){
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[SN Fail]";
    errorIcons.push({
      type: 'serial',
      icon: '🔢',
      message: 'Invalid Serial Number'
    });
  }

  // 檢查電池電壓
  console.log('電壓檢測 V: ' + batteryData.voltage);
  if(BatteryValidator.isVoltageLowerThanLimit(batteryData.voltage, getBatteryType(batteryData.sn))) {
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[PackVoltage Fail]";
    errorIcons.push({
      type: 'packVoltage',
      icon: '⚡',
      message: 'Pack Voltage Too Low'
    });
  }

  // CheckHealth
  if(BatteryValidator.isHealthLowerThanLimit(batteryDataHelper.getBatteryHealth(batteryData.fullyChargedCapacity, batteryData.designCapacity))) {
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[Health Fail]";
    errorIcons.push({
      type: 'health',
      icon: '💔',
      message: 'Battery Health Too Low'
    });
  }
  // CellVoltage
  const cellVoltages = [
    { voltage: batteryData.cellVoltage_1, name: 'Cell 1' },
    { voltage: batteryData.cellVoltage_2, name: 'Cell 2' },
    { voltage: batteryData.cellVoltage_3, name: 'Cell 3' },
    { voltage: batteryData.cellVoltage_4, name: 'Cell 4' }
  ];

  cellVoltages.forEach((cell, index) => {
    if (cell.voltage && BatteryValidator.IsCellVoltageLowerThanLimit(cell.voltage)) {
      isValid = false;
      //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
      errorDescription += `[CellVolt${index + 1} Fail]`;
      errorIcons.push({
        type: `cellVolt${index + 1}`,
        icon: '🔋',
        message: `${cell.name} Voltage Too Low`
      });
    }
  });

  // CheckPFFlag
  if(getBatteryType(batteryData.sn) === BatteryType.XL ||
     getBatteryType(batteryData.sn) === BatteryType.Standard ||
     getBatteryType(batteryData.sn) === BatteryType.Mini){

      if(BatteryValidator.IsPfFlagRaised(batteryData.diagParamPfFlag)){
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[PF Fail]";
        errorIcons.push({
          type: 'pfFlag',
          icon: '🚨',
          message: 'PF Flag Raised'
        });
      }
  }

  // CheckCycleCount
  if(BatteryValidator.IsCycleCountOverThanLimit(batteryData.cycle)){
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[CycleCount Fail]";
    errorIcons.push({
      type: 'cycleCount',
      icon: '🔄',
      message: 'Cycle Count Exceeded'
    });
  }

  // CheckUnbalance
  let [isUnbalance, voltDiff] = BatteryValidator.IsBatteryUnbalance(batteryData);
  if(isUnbalance){
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[CellVolt Unbalance。(voltDiff: " + voltDiff.toFixed(2) + ")]";
    errorIcons.push({
      type: 'unbalance',
      icon: '⚖️',
      message: `Cell Voltage Unbalance (${voltDiff.toFixed(2)}mV)`
    });
  }

  // CheckTemperature
  if(!BatteryValidator.IsTemperatureValid(batteryData.temperature)){
    isValid = false;
    //batteryClass = 'battery-low-image';
    batteryClass = 'verify-error';
    errorDescription += "[Temperature Fail]";
    errorIcons.push({
      type: 'temperature',
      icon: '🌡️',
      message: 'Temperature Too High'
    });
  }

  
  return {
    isValid,
    batteryClass,
    errorIcons,
    errorDescription
  };
}

// 更新電池顯示
function updateBatteryDisplay(batteryDataList: BatteryData[]) {
  currentBatteryData = batteryDataList;
  console.log("batteryDataList Data:", batteryDataList);
  console.log("currentBatteryData Data:", currentBatteryData);
  
  // 電池排列順序：5 3 1 / 4 2 0
  const batteryOrder = [5, 3, 1, 4, 2, 0];

  // 根據指定順序創建電池卡片
  batteryOrder.forEach(index => {
    const batteryData = batteryDataList.find(battery => battery.id === index);
    console.log(`Battery ${index}: ID=${batteryData?.id}, SN=${batteryData?.sn}`);
    if(batteryData && batteryData?.sn !== "") {
      console.log(`insertBattery ${index}: ID=${batteryData?.id}, SN=${batteryData?.sn}`);
      insertBattery(index, batteryData);
    }else{
      console.log(`removeBattery ${index}`);
      removeBattery(index);
    }

    //const batteryCard = createBatteryCard(index, batteryData);
    //batteryGrid.appendChild(batteryCard);
  });

  // 更新統計資訊
  updateStatistics(batteryDataList);
}

// 查看電池是否正確取得資訊
function isBatteryComplete(m_fullyChargedCapacity: number, m_remainingCapacity: number, m_deviceName: string): boolean
{
    try
    {
      if(m_fullyChargedCapacity !== 0 && m_remainingCapacity !== 0 && m_deviceName !== ""){
        return true;  
      }
    }
    catch (error)
    {
        console.error("計算電池健康狀況時發生錯誤:", error);
        return false;
    }
    return false;
}



// 從 localStorage 讀取 Area 資訊
function getBatteryAreaInfo(): BatteryAreaInfo {
  try {
    const stored = localStorage.getItem('batteryAreaInfo');
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('讀取 Area 資訊失敗:', error);
    return {};
  }
}

// 儲存 Area 資訊到 localStorage
function saveBatteryAreaInfo(areaInfo: BatteryAreaInfo): void {
  try {
    localStorage.setItem('batteryAreaInfo', JSON.stringify(areaInfo));
  } catch (error) {
    console.error('儲存 Area 資訊失敗:', error);
  }
}

// 設定特定電池的 Area 資訊
function setBatteryArea(batteryId: string, area: string): void {
  const areaInfo = getBatteryAreaInfo();
  areaInfo[batteryId] = area;
  saveBatteryAreaInfo(areaInfo);
}

// 獲取特定電池的 Area 資訊
function getBatteryArea(batteryId: string): string {
  const areaInfo = getBatteryAreaInfo();
  return areaInfo[batteryId] || '未設定';
}



// 從 localStorage 讀取毀損狀態資訊
function getBatteryDamageInfo(): BatteryDamageInfo {
  try {
    const stored = localStorage.getItem('batteryDamageInfo');
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('讀取毀損狀態資訊失敗:', error);
    return {};
  }
}

// 儲存毀損狀態資訊到 localStorage
function saveBatteryDamageInfo(damageInfo: BatteryDamageInfo): void {
  try {
    localStorage.setItem('batteryDamageInfo', JSON.stringify(damageInfo));
  } catch (error) {
    console.error('儲存毀損狀態資訊失敗:', error);
  }
}

// 設定特定電池的毀損狀態
function setBatteryDamage(batteryId: string, isDamaged: boolean): void {
  const damageInfo = getBatteryDamageInfo();
  damageInfo[batteryId] = isDamaged;
  saveBatteryDamageInfo(damageInfo);
}

// 獲取特定電池的毀損狀態
function getBatteryDamage(batteryId: string): boolean {
  const damageInfo = getBatteryDamageInfo();
  return damageInfo[batteryId] || false;
}

// 創建電池卡片
async function createBatteryCard(slotId: number, batteryData: BatteryData): Promise<HTMLElement> {
  const card = document.createElement('div');
  const isBatteryCompleted = isBatteryComplete(batteryData?.fullyChargedCapacity ?? 0, batteryData?.remainingCapacity ?? 0, batteryData?.deviceName ?? '');
  
  // 執行電池驗證以確定是否有錯誤
  if(isBatteryCompleted) {
    //const verificationResult = batteryData ? verifyBatteryData(batteryData) : { errorIcons: [] };
    const verificationResult = verifyBatteryData(batteryData);
    const hasErrors = verificationResult.errorIcons.length > 0;

    card.className = `battery-card ${!batteryData || batteryData.sn === '' ? 'no-battery' : ''} ${hasErrors ? 'has-errors' : ''}`;

    // Add Tooltip Listen
    if (batteryData && batteryData.sn !== '') {
      card.addEventListener('mouseenter', (e) => showBatteryTooltip(e, batteryData, slotId));
      card.addEventListener('mouseleave', hideBatteryTooltip);
    }

    const batteryHealth = batteryDataHelper.getBatteryHealth(batteryData?.fullyChargedCapacity || 0, batteryData?.designCapacity || 0);
    if(batteryHealth < 0 || batteryHealth > 100) {
      console.error("無效的電池健康值:", batteryHealth);
    }

    //let imgRotate = 0;
    let infoClass = "battery-info-top", snClass = "serial-number-top";
    if(slotId === 1 || slotId === 3 || slotId === 5) { 
      //imgRotate = 0; 
      infoClass = "battery-info-top";
      snClass = "serial-number-top";
    }
    if(slotId === 0 || slotId === 2 || slotId === 4) { 
      //imgRotate = 180; 
      infoClass = "battery-info-bottom";
      snClass = "serial-number-bottom";
    }
    
    // 獲取電池的 Area 資訊
    const batteryArea = getBatteryArea(batteryData?.sn || '');

    // 檢查毀損狀態
    const isDamaged = getBatteryDamage(batteryData?.sn || '');

    // 如果有電池資料且控制器已初始化，則更新電池資料
    // alert("更新資料");
    if (batteryData && diagnosticsController) {
      diagnosticsController.updateBatteryData(batteryData, verificationResult.errorDescription, verificationResult.isValid);
    }

    // 生成錯誤圖示 HTML（使用之前已經執行的驗證結果）
    const errorIconsHtml = verificationResult.errorIcons.length > 0
      ? `<div class="error-icons">
           ${verificationResult.errorIcons.map(error =>
             `<span class="error-icon" title="${error.message}">${error.icon}</span>`
           ).join('')}
         </div>`
      : '';

    // 健康度指示器類別
    // const healthClass = batteryHealth >= 80 ? 'health-good' :
    //                    batteryHealth >= 60 ? 'health-medium' : 'health-poor';

    card.innerHTML = `
      <div class="battery-info-card ${infoClass}" id="info${slotId}" style="display: flex; flex-direction: column; height: 100%;">
          <div class="serial-number ${snClass}">
            ${batteryData?.sn}
          </div>

          <div class="detail-info" style="display: flex; align-items: center; margin-bottom: 4px; gap: 4px;">
            <span style="font-size: 10px;">Area:</span>
            <input
              type="text"
              id="area-quick-input-${batteryData?.id}"
              value="${batteryArea === '未設定' ? '' : batteryArea}"
              placeholder="設定區域"
              style="flex: 1; padding: 2px 4px; background: rgba(55, 65, 81, 0.7); border: 1px solid #4b5563; border-radius: 3px; color: #f3f4f6; font-size: 10px; min-width: 60px;"
              onchange="updateQuickArea('${batteryData?.sn}', this.value)"
            />
          </div>

          <div style="flex: 1;">
            ${errorIconsHtml}
          </div>

          <div style="display: flex; gap: 4px; margin-top: auto;">
            <button
              id="damage-btn-${batteryData?.id}"
              onclick="toggleBatteryDamage('${batteryData?.sn}', '${batteryData?.id}')"
              style="flex: 1; padding: 2px 6px; background: ${isDamaged ? 'linear-gradient(135deg, #ef4444, #dc2626)' : 'linear-gradient(135deg, #6b7280, #4b5563)'}; color: white; border: none; border-radius: 3px; font-size: 9px; cursor: pointer; transition: all 0.3s ease;"
              title="${isDamaged ? 'Click to restore normal status' : 'Click to mark as damaged'}"
            >
              ${isDamaged ? 'Damaged' : 'Mark Damage'}
            </button>
            <button class="details-btn" onclick="showDetails(${batteryData?.id})" style="flex: 1; padding: 2px 6px; font-size: 9px;">
              Details
            </button>
          </div>
      </div>
    `;
  } else {
    card.className = `battery-card no-battery no-info`;
    card.innerHTML = `
      <div class="battery-info" id="info${slotId}">
          <p>Loading...</p>
      </div>
    `;
  }
  
  return card;
}

// 工具提示相關功能
let tooltipElement: HTMLElement | null = null;

function showBatteryTooltip(event: MouseEvent, batteryData: BatteryData, slotId: number): void {
  // 移除現有的工具提示
  hideBatteryTooltip();

  const batteryHealth = batteryDataHelper.getBatteryHealth(batteryData.fullyChargedCapacity, batteryData.designCapacity);
  const batteryArea = getBatteryArea(batteryData.sn || '');

  // 判斷是上排還是下排電池
  // 上排電池：slotId 為 1, 3, 5
  // 下排電池：slotId 為 0, 2, 4
  const isTopRow = [1, 3, 5].includes(batteryData.id);

  // 創建工具提示元素
  tooltipElement = document.createElement('div');
  tooltipElement.className = 'battery-tooltip show';

  // 根據位置調整箭頭樣式
  const arrowStyle = isTopRow ?
    `content: '';
     position: absolute;
     bottom: -6px;
     left: 20px;
     width: 0;
     height: 0;
     border-left: 6px solid transparent;
     border-right: 6px solid transparent;
     border-top: 6px solid rgba(17, 24, 39, 0.95);` :
    `content: '';
     position: absolute;
     top: -6px;
     left: 20px;
     width: 0;
     height: 0;
     border-left: 6px solid transparent;
     border-right: 6px solid transparent;
     border-bottom: 6px solid rgba(17, 24, 39, 0.95);`;

  tooltipElement.innerHTML = `
    <style>
      .battery-tooltip-${batteryData.id}::${isTopRow ? 'after' : 'before'} {
        ${arrowStyle}
      }
    </style>
    <div class="battery-tooltip-${batteryData.id} battery-${slotId}" style="font-weight: bold; margin-bottom: 8px; color: #60a5fa;">電池詳細資訊</div>
    <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px 12px; font-size: 11px;">
      <span style="color: #9ca3af;">序號:</span>
      <span style="color: #f3f4f6;">${batteryData.sn}</span>

      <span style="color: #9ca3af;">健康度:</span>
      <span style="color: ${batteryHealth >= 80 ? '#10b981' : batteryHealth >= 50 ? '#f59e0b' : '#ef4444'};">
        ${batteryHealth.toFixed(1)}%
      </span>

      <span style="color: #9ca3af;">循環次數:</span>
      <span style="color: #f3f4f6;">${batteryData.cycle}</span>

      <span style="color: #9ca3af;">區域:</span>
      <span style="color: #f3f4f6;">${batteryArea}</span>

      <span style="color: #9ca3af;">電流:</span>
      <span style="color: #f3f4f6;">${batteryData.current} mA</span>

      <span style="color: #9ca3af;">電壓:</span>
      <span style="color: #f3f4f6;">${batteryData.voltage} mV</span>

      <span style="color: #9ca3af;">溫度:</span>
      <span style="color: #f3f4f6;">${batteryData.temperature.toFixed(1)}°C</span>

      <span style="color: #9ca3af;">電量:</span>
      <span style="color: #f3f4f6;">${batteryData.relativeStateOfCharged}%</span>
    </div>
  `;

  document.body.appendChild(tooltipElement);

  // 定位工具提示
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const tooltipRect = tooltipElement.getBoundingClientRect();
  console.log(`定位工具提示: rect=${JSON.stringify(rect)}, tooltipRect=${JSON.stringify(tooltipRect)}`);
  
  let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
  let top: number;

  if (isTopRow) {
    // 上排電池：工具提示顯示在電池上方
    top = rect.top - tooltipRect.height - 10;
  } else {
    // 下排電池：工具提示顯示在電池下方
    top = rect.bottom + 10;
  }

  // // 確保工具提示不會超出視窗左右邊界
  // if (left < 10) left = 10;
  // if (left + tooltipRect.width > window.innerWidth - 10) {
  //   left = window.innerWidth - tooltipRect.width - 10;
  // }

  // // 確保工具提示不會超出視窗上下邊界
  // if (top < 10) {
  //   top = 10;
  // } else if (top + tooltipRect.height > window.innerHeight - 10) {
  //   top = window.innerHeight - tooltipRect.height - 10;
  // }
  // top += 100;

  tooltipElement.style.left = `${left}px`;
  tooltipElement.style.top = `${top}px`;
}

function hideBatteryTooltip(): void {
  if (tooltipElement) {
    tooltipElement.remove();
    tooltipElement = null;
  }
}

// 顯示電池詳細資訊
// (window as any).showDiagnosticsBatteryDetails = function(slotId: number) {
//   const batteryData = currentBatteryData.find(battery => battery.id === slotId);
//   if (!batteryData || batteryData.sn === '') return;

//   const modal = document.getElementById('battery-details-modal');
//   const content = document.getElementById('battery-details-content');
  
//   if (!modal || !content) return;

//   content.innerHTML = `
//     <div class="details-grid">
//       <div class="detail-section">
//         <h4>基本資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">序號:</span>
//           <span class="detail-value">${batteryData.sn}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">設備名稱:</span>
//           <span class="detail-value">${batteryData.deviceName || 'N/A'}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">製造日期:</span>
//           <span class="detail-value">${batteryData.manufactureDate || 'N/A'}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">首次使用:</span>
//           <span class="detail-value">${batteryData.firstUseDate || 'N/A'}</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電量資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">相對電量:</span>
//           <span class="detail-value">${batteryData.relativeStateOfCharged}%</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">絕對電量:</span>
//           <span class="detail-value">${batteryData.absoluteStateOfCharged}%</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">剩餘容量:</span>
//           <span class="detail-value">${batteryData.remainingCapacity} mAh</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">滿充容量:</span>
//           <span class="detail-value">${batteryData.fullyChargedCapacity} mAh</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">設計容量:</span>
//           <span class="detail-value">${batteryData.designCapacity} mAh</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電壓電流</h4>
//         <div class="detail-item">
//           <span class="detail-label">電壓:</span>
//           <span class="detail-value">${batteryData.voltage} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電流:</span>
//           <span class="detail-value">${batteryData.current} mA</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">平均電流:</span>
//           <span class="detail-value">${batteryData.averageCurrent} mA</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電電壓:</span>
//           <span class="detail-value">${batteryData.chargingVoltage} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電電流:</span>
//           <span class="detail-value">${batteryData.chargingCurrent} mA</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>溫度與循環</h4>
//         <div class="detail-item">
//           <span class="detail-label">溫度:</span>
//           <span class="detail-value">${batteryData.temperature.toFixed(2)} °C</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環次數:</span>
//           <span class="detail-value">${batteryData.cycle}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環指數:</span>
//           <span class="detail-value">${batteryData.cycleIndex}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環閾值:</span>
//           <span class="detail-value">${batteryData.cycleThreshold}</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電芯電壓</h4>
//         <div class="detail-item">
//           <span class="detail-label">電芯1:</span>
//           <span class="detail-value">${batteryData.cellVoltage_1} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯2:</span>
//           <span class="detail-value">${batteryData.cellVoltage_2} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯3:</span>
//           <span class="detail-value">${batteryData.cellVoltage_3} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯4:</span>
//           <span class="detail-value">${batteryData.cellVoltage_4} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">總電壓:</span>
//           <span class="detail-value">${batteryData.packVoltage} mV</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>狀態資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">運行狀態:</span>
//           <span class="detail-value">${batteryData.operationStatus}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電狀態:</span>
//           <span class="detail-value">${batteryData.chargingStatus}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">溫度範圍:</span>
//           <span class="detail-value">${batteryData.temperatureRange}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">錯誤代碼:</span>
//           <span class="detail-value">${batteryData.error}</span>
//         </div>
//       </div>
//     </div>
//   `;

//   modal.classList.remove('hidden');
// };

// 更新統計資訊
function updateStatistics(batteryDataList: BatteryData[]) {
  const totalElement = document.getElementById("total-batteries");
  const normalElement = document.getElementById("normal-batteries");
  const incompleteElement = document.getElementById("incomplete-batteries");
  const errorElement = document.getElementById("error-batteries");

  // 過濾有效電池（有序號的電池）
  const validBatteries = batteryDataList.filter(battery => battery.sn !== '');
  
  if (totalElement) totalElement.textContent = validBatteries.length.toString();

  // 計算各種狀態的電池數量
  let normalCount = 0;
  let incompleteCount = 0;
  let errorCount = 0;

  validBatteries.forEach(battery => {
    //const batteryLevel = battery.relativeStateOfCharged || 0;
    //const current = battery.current || 0;
    const batteryHealth = batteryDataHelper.getBatteryHealth(battery?.fullyChargedCapacity ?? 0, battery?.designCapacity ?? 0)

    if (isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity, battery?.deviceName)) {
      console.log("completeCount ++");      
      console.log("batteryHealth: " + batteryHealth);
      console.log("isBatteryComplete: " + isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity, battery?.deviceName));
      if (batteryHealth < 50) {
        errorCount++; // 電池不健康
      } else {
        normalCount++; // 正常電池
      }
    } else {
      console.log("incompleteCount ++");
      console.log("batteryHealth: " + batteryHealth);
      console.log("isBatteryComplete: " + isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity, battery?.deviceName));
      incompleteCount++; // 資料錯誤
    }
  });

  if (normalElement) normalElement.textContent = normalCount.toString();
  if (incompleteElement) incompleteElement.textContent = incompleteCount.toString();
  if (errorElement) errorElement.textContent = errorCount.toString();
}

export async function get_batteries(){
  // 儀表板頁面的初始化邏輯

  // 添加錯誤圖示樣式
  addErrorIconStyles();

  // 初始化 DiagnosticsViewController
  if (!diagnosticsController) {
    diagnosticsController = new DiagnosticsViewController();
    await diagnosticsController.initialize();
  }

  try {
    if (await invoke("LOAD_SDK")) {
      listen("USB_ChangedEvent", async () => {
        Initial();
      });
      
      Initial();
      
      // 設置關閉詳細資訊彈窗的事件監聽器
      const closeBtn = document.getElementById('close-details-btn');
      const modal = document.getElementById('battery-details-modal');
      
      if (closeBtn && modal) {
        closeBtn.addEventListener('click', () => {
          modal.classList.add('hidden');
        });
      }

      // 點擊彈窗外部關閉
      if (modal) {
        modal.addEventListener('click', (e) => {
          if (e.target === modal) {
            modal.classList.add('hidden');
          }
        });
      }
    }
  }
  catch (error) {
    console.error("Failed to load library:", error);
  }
}

// 顯示電池詳細資訊
(window as any).showDetails = function(slotId: number) {
  //alert("showBatteryDetails");
  //alert("slotId: " + slotId);
  console.log("batteries: ", currentBatteryData);
  const batteryData = currentBatteryData.find(battery => battery.id === slotId);
  console.log("battery: ", batteryData);
  if (!batteryData || batteryData.sn === '') return;

  const modal = document.getElementById('battery-details-modal');
  const content = document.getElementById('battery-details-content');
  
  if (!modal || !content) return;

  let batteryPackVoltage = "NA";
  if(getBatteryType(batteryData.sn) === BatteryType.XL || getBatteryType(batteryData.sn) === BatteryType.Standard) {
    batteryPackVoltage = batteryData.packVoltage.toString() || "NA";
  }

  // 檢查電池類型是否為 XXL
  const isXXLBattery = getBatteryType(batteryData.sn) === BatteryType.XXL;

  content.innerHTML = `
    <div class="details-grid">
      <div class="detail-section">
        <h4>📦基本資訊</h4>
        <div class="detail-item">
          <span class="detail-label">ID:</span>
          <span class="detail-value">${batteryData.id}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SN 序號:</span>
          <span class="detail-value">${batteryData.sn}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Battery Type 電池類型:</span>
          <span class="detail-value">${getBatteryTypeText(batteryData.sn)}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PACK 電池包數量:</span>
          <span class="detail-value">${batteryData.pack}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">MODE 模式碼:</span>
          <span class="detail-value">${batteryData.mode}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🔋電池資訊與製造區塊</h4>
        <div class="detail-item">
          <span class="detail-label">Device Name 設備名稱:</span>
          <span class="detail-value">${batteryData.deviceName || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">GaugeStatus 電量計狀態碼:</span>
          <span class="detail-value">${batteryData.gaugeStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Error 錯誤代碼:</span>
          <span class="detail-value">${batteryData.error}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureDate 製造日期:</span>
          <span class="detail-value">${batteryData.manufactureDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_1 製造資訊區塊1:</span>
          <span class="detail-value">${batteryData.manufactureBlock_1 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_2 製造資訊區塊2:</span>
          <span class="detail-value">${batteryData.manufactureBlock_2 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_3 製造資訊區塊3:</span>
          <span class="detail-value">${batteryData.manufactureBlock_3 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_4 製造資訊區塊4:</span>
          <span class="detail-value">${batteryData.manufactureBlock_4 || 'N/A'}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>📊電量資訊</h4>
        <div class="detail-item">
          <span class="detail-label">RelativeStateOfCharged 相對電量:</span>
          <span class="detail-value">${batteryData.relativeStateOfCharged} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">AbsoluteStateOfCharged 絕對電量:</span>
          <span class="detail-value">${batteryData.absoluteStateOfCharged} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RemainingCapacity 剩餘容量:</span>
          <span class="detail-value">${batteryData.remainingCapacity} mAh</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacity 滿充容量:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacity} mAh</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">DesignVoltage 設計電壓:</span>
          <span class="detail-value">${batteryData.designVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">DesignCapacity 設計容量:</span>
          <span class="detail-value">${batteryData.designCapacity} mAh</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>⚡ 電壓電流</h4>
        <div class="detail-item">
          <span class="detail-label">Voltage 電壓:</span>
          <span class="detail-value">${batteryData.voltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Current 電流:</span>
          <span class="detail-value">${batteryData.current} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">AverageCurrent 平均電流:</span>
          <span class="detail-value">${batteryData.averageCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingVoltage 充電電壓:</span>
          <span class="detail-value">${batteryData.chargingVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingCurrent 充電電流:</span>
          <span class="detail-value">${batteryData.chargingCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">UntilFullyCharged 距離充滿所需時間:</span>
          <span class="detail-value">${batteryData.untilFullyCharged} min</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">UntilFullyDischarged 距離放完電時間:</span>
          <span class="detail-value">${batteryData.untilFullyDischarged} min</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🌡️ 溫度與循環</h4>
        <div class="detail-item">
          <span class="detail-label">Temperature 溫度:</span>
          <span class="detail-value">${batteryData.temperature.toFixed(2)} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Cycle 循環次數:</span>
          <span class="detail-value">${batteryData.cycle}</span>
        </div>
      </div>

      ${isXXLBattery ? `
      <div class="detail-section">
        <h4>🔄 延伸資料 (XXL)</h4>
        <div class="detail-item">
          <span class="detail-label">CycleIndex 循環指數:</span>
          <span class="detail-value">${batteryData.cycleIndex}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CycleThreshold 循環閾值:</span>
          <span class="detail-value">${batteryData.cycleThreshold}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedDate 最近一次充滿日期:</span>
          <span class="detail-value">${batteryData.fullyChargedDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacityThreshold 完充容量門檻值:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacityThreshold}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacityBackup 完充容量備份值:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacityBackup}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMaxPackVoltage 生命週期中最大整包電壓:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMaxPackVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMinPackVoltage 最小整包電壓:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMinPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMaxCurrent 最大電流:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMaxCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMinCurrent 最小電流:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMinCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OrangeLED 橘燈狀態:</span>
          <span class="detail-value">${batteryData.orangeLED}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedVoltage 完全充電時電壓:</span>
          <span class="detail-value">${batteryData.fullyChargedVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FirstUseDate 首次使用日期:</span>
          <span class="detail-value">${batteryData.firstUseDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RecordDate 紀錄日期:</span>
          <span class="detail-value">${batteryData.recordDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RecordTime 紀錄時間:</span>
          <span class="detail-value">${batteryData.recordTime || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PackMode 電池包模式:</span>
          <span class="detail-value">${batteryData.packMode}</span>
        </div>
      </div>
      ` : ''}

      <div class="detail-section">
        <h4>🔋 電芯與電壓資訊</h4>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_1 電芯1:</span>
          <span class="detail-value">${batteryData.cellVoltage_1} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_2 電芯2:</span>
          <span class="detail-value">${batteryData.cellVoltage_2} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_3 電芯3:</span>
          <span class="detail-value">${batteryData.cellVoltage_3} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_4 電芯4:</span>
          <span class="detail-value">${batteryData.cellVoltage_4} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PackVoltage 電池包總電壓:</span>
          <span class="detail-value">${batteryPackVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FETControl 開關控制狀態:</span>
          <span class="detail-value">${batteryData.fetControl}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🛡️ 安全與錯誤狀態</h4>
        <div class="detail-item">
          <span class="detail-label">SafetyAlert_1 安全警報:</span>
          <span class="detail-value">${batteryData.safetyAlert_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyAlert_2 安全警報:</span>
          <span class="detail-value">${batteryData.safetyAlert_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyStatus_1 安全狀態:</span>
          <span class="detail-value">${batteryData.safetyStatus_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyStatus_2 安全狀態:</span>
          <span class="detail-value">${batteryData.safetyStatus_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFAlert_1 永久失效警報:</span>
          <span class="detail-value">${batteryData.pfAlert_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFAlert_2 永久失效警報:</span>
          <span class="detail-value">${batteryData.pfAlert_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFStatus_1 永久失效狀態:</span>
          <span class="detail-value">${batteryData.pfStatus_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFStatus_2 永久失效狀態:</span>
          <span class="detail-value">${batteryData.pfStatus_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OperationStatus 運行狀態 操作狀態碼:</span>
          <span class="detail-value">${batteryData.operationStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingStatus 充電狀態碼:</span>
          <span class="detail-value">${batteryData.chargingStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">TemperatureRange 溫度範圍:</span>
          <span class="detail-value">${batteryData.temperatureRange}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">MaxError 最大誤差:</span>
          <span class="detail-value">${batteryData.maxError} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxTemperature 使用過程中最高溫度:</span>
          <span class="detail-value">${batteryData.lifetimeMaxTemperature} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinTemperature 最低溫度:</span>
          <span class="detail-value">${batteryData.lifetimeMinTemperature} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeAvgTemperature 平均溫度:</span>
          <span class="detail-value">${batteryData.lifetimeAvgTemperature}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxCellVoltage 最大單顆電芯電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMaxCellVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinCellVoltage 最小單顆電芯電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMinCellVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxPackVoltage 最大整包電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMaxPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinPackVoltage 最小整包電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMinPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxChargingCurrent 最大充電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxChargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxDischargingCurrent 最大放電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxDischargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxAvgDischargingCurrent 最大平均放電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxChargingPower 最大充電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxChargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxDischargingPower 最大放電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxDischargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxAvgDischargingPower 最大平均放電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeTemperatureSamples 溫度樣本總數:</span>
          <span class="detail-value">${batteryData.lifetimeTemperatureSamples}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OTEventCount 過溫事件次數:</span>
          <span class="detail-value">${batteryData.otEventCount}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OTEventDuration 過溫事件總時長:</span>
          <span class="detail-value">${batteryData.otEventDuration}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OVEventCount 過壓事件次數:</span>
          <span class="detail-value">${batteryData.ovEventCount}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OVEventDuration 過壓事件總時長:</span>
          <span class="detail-value">${batteryData.ovEventDuration}</span>
        </div>

      </div>

      <div class="detail-section">
        <h4>📍 區域設定</h4>
        <div class="detail-item">
          <span class="detail-label">目前區域:</span>
          <span class="detail-value" id="current-area-${batteryData.id}">${getBatteryArea(batteryData.sn)}</span>
        </div>
        <div class="detail-item" style="flex-direction: column; align-items: stretch; gap: 8px;">
          <label class="detail-label" for="area-input-${batteryData.id}">設定新區域:</label>
          <div style="display: flex; gap: 8px;">
            <input
              type="text"
              id="area-input-${batteryData.id}"
              placeholder="輸入區域名稱..."
              value="${getBatteryArea(batteryData.sn) === '未設定' ? '' : getBatteryArea(batteryData.sn)}"
              style="flex: 1; padding: 6px 8px; background: rgba(55, 65, 81, 0.5); border: 1px solid #4b5563; border-radius: 4px; color: #f3f4f6; font-size: 14px;"
            />
            <button
              onclick="updateBatteryArea('${batteryData.sn}', '${batteryData.id}')"
              style="padding: 6px 12px; background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; border-radius: 4px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;"
              onmouseover="this.style.background='linear-gradient(135deg, #059669, #047857)'"
              onmouseout="this.style.background='linear-gradient(135deg, #10b981, #059669)'"
            >
              儲存
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  modal.classList.remove('hidden');
};

// 快速更新電池區域資訊
(window as any).updateQuickArea = function(batterySn: string, newArea: string) {
  const area = newArea.trim() || '未設定';

  // 儲存到 localStorage
  setBatteryArea(batterySn, area);

  // 更新電池卡片顯示
  updateBatteryDisplay(currentBatteryData);
};

// 切換電池毀損狀態
(window as any).toggleBatteryDamage = function(batterySn: string, batteryId: string) {
  const currentDamage = getBatteryDamage(batterySn);
  const newDamage = !currentDamage;

  // 儲存毀損狀態
  setBatteryDamage(batterySn, newDamage);

  // 更新電池卡片顯示
  updateBatteryDisplay(currentBatteryData);

  // 更新按鈕顯示
  const button = document.getElementById(`damage-btn-${batteryId}`) as HTMLButtonElement;
  if (button) {
    button.textContent = newDamage ? 'Damaged' : 'Mark Damage';
    button.style.background = newDamage ?
      'linear-gradient(135deg, #ef4444, #dc2626)' :
      'linear-gradient(135deg, #6b7280, #4b5563)';
    button.title = newDamage ? 'Click to restore normal status' : 'Click to mark as damaged';
  }
};

// 更新電池區域資訊
(window as any).updateBatteryArea = function(batterySn: string, batteryId: string) {
  const input = document.getElementById(`area-input-${batteryId}`) as HTMLInputElement;
  const currentAreaSpan = document.getElementById(`current-area-${batteryId}`);

  if (!input || !currentAreaSpan) return;

  const newArea = input.value.trim() || '未設定';

  // 儲存到 localStorage
  setBatteryArea(batterySn, newArea);

  // 更新顯示
  currentAreaSpan.textContent = newArea;

  // 更新電池卡片顯示
  updateBatteryDisplay(currentBatteryData);

  // 顯示成功訊息
  const button = input.nextElementSibling as HTMLButtonElement;
  const originalText = button.textContent;
  button.textContent = '已儲存!';
  button.style.background = 'linear-gradient(135deg, #059669, #047857)';

  setTimeout(() => {
    button.textContent = originalText;
    button.style.background = 'linear-gradient(135deg, #10b981, #059669)';
  }, 1500);
};




// // 初始化控制器
// window.addEventListener('DOMContentLoaded', () => {
//   // 儀表板頁面的初始化邏輯
//   console.log("DOMContentLoaded");
  
// });

// window.addEventListener("DOMContentLoaded", async () => {
//   console.log("DOMContentLoaded async");
// });

// (function() {
//   // 儀表板頁面的初始化邏輯
//   console.log("初始化設定頁面");
//   const controller = new BatteryViewController();
//   controller.initialize();
// })();


// 電池管理
export class DiagnosticsViewController {
  private apiService: BatteryApiService;
  private chargerApiService: ChargerApiService;
  //private batteriesContainer: HTMLElement | null;
  
  constructor() {
    this.apiService = new BatteryApiService();
    this.chargerApiService = new ChargerApiService();
    //this.batteriesContainer = document.getElementById("batteries-container");
  }

  // 初始化
  async initialize() {
    //await this.loadBatteries();
    //this.setupEventListeners();
  }


  async updateBatteryData(batteryData: BatteryData, errorDescription: string, isValid: boolean) {
    let success = false;
    /*
    if (batteryId) {
      // 更新電池
      success = await this.apiService.updateBattery(batteryId, batteryData);
    } else {
      // 新增電池
      const newBattery = await this.apiService.addBattery(batteryData);
      success = !!newBattery;
    }
    */
    // alert("更新電池資料 call api");
     // 將 BatteryData 轉成 BatteryDataOut（多加 charger_SN 欄位）
    let validResult = isValid && !getBatteryDamage(batteryData?.sn || '') ? true : false;
    const batteryDataOut: BatteryDataOut = {
      ...batteryData,
      
      chargerSN: tbxSerialNumber.toString(), // 充電器序列號
      typeText: getBatteryTypeText(batteryData.sn), // 電池類型文字
      maxCellVoltage: batteryDataHelper.getMaxCellVoltage(batteryData),
      minCellVoltage: batteryDataHelper.getMinCellVoltage(batteryData),
      isDamaged: getBatteryDamage(batteryData?.sn || ''), // 是否損壞
      health: batteryDataHelper.getBatteryHealth(batteryData?.fullyChargedCapacity || 0, batteryData?.designCapacity || 0), // 電池健康狀況百分比
      pfFlag: BatteryValidator.IsPfFlagRaised(batteryData.diagParamPfFlag), // 是否有 PFFail // Pass/Fail
      errorStatus: batteryData.error.toString(16).toUpperCase().padStart(4, '0'), // 將數字轉為四位數的十六進位字串，不足補零
      errorMessage: errorDescription, // 錯誤信息
      result: validResult, // 是否成功 // Pass/Fail
      location: getBatteryArea(batteryData.sn), // 位置
      comment: "", // 備註
    };


    const newBattery = await this.apiService.addBattery(batteryDataOut);
    success = !!newBattery;
    if (success) {
    } else {
      alert("updateBatteryData 操作失敗，請稍後再試");
    }
  }

  async updateChargerData(chargerData: ChargerData) {
    let success = false;


    const newCharger = await this.chargerApiService.addCharger(chargerData);
    success = !!newCharger;
    if (success) {
    } else {
      alert("updateChargerData 操作失敗，請稍後再試");
    }
  }

}